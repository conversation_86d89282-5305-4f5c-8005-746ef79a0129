<?php

namespace app\admin\validate;

use think\Validate;

class Rider extends Validate
{
    /**
     * 验证规则
     */
    protected $rule = [
        'real_name' => 'require',
        'mobile' => 'require|unique:rider',
        'password' => 'require'
    ];
    /**
     * 提示消息
     */
    protected $message = [
        'real_name.require' => '真实姓名不能为空',
        'mobile.require' => '手机号不能为空',
        'mobile.unique' => '手机号已存在',
        'password.require' => '密码不能为空'
    ];
    /**
     * 验证场景
     */
    protected $scene = [
        'add'  => ['real_name', 'mobile', 'password'],
        'edit' => ['real_name'],
    ];

}
