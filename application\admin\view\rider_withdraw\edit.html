<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Agent_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-agent_id" data-rule="required" data-source="agent/index" class="form-control selectpage" name="row[agent_id]" type="text" value="{$row.agent_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Rider_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-rider_id" data-rule="required" data-source="rider/index" class="form-control selectpage" name="row[rider_id]" type="text" value="{$row.rider_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Order_sn')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-order_sn" class="form-control" name="row[order_sn]" type="text" value="{$row.order_sn|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Apply_type')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-apply_type" class="form-control selectpicker" name="row[apply_type]">
                {foreach name="applyTypeList" item="vo"}
                    <option value="{$key}" {in name="key" value="$row.apply_type"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Amount')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-amount" class="form-control" step="0.01" name="row[amount]" type="number" value="{$row.amount|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Real_amount')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-real_amount" class="form-control" step="0.01" name="row[real_amount]" type="number" value="{$row.real_amount|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Service_fee_amount')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-service_fee_amount" class="form-control" step="0.01" name="row[service_fee_amount]" type="number" value="{$row.service_fee_amount|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Service_fee_rate')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-service_fee_rate" class="form-control" step="0.001" name="row[service_fee_rate]" type="number" value="{$row.service_fee_rate|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Username')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-username" class="form-control" name="row[username]" type="text" value="{$row.username|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Card_no')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-card_no" class="form-control" name="row[card_no]" type="text" value="{$row.card_no|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Apply_info')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-apply_info" class="form-control" name="row[apply_info]" type="text" value="{$row.apply_info|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            
            <div class="radio">
            {foreach name="statusList" item="vo"}
            <label for="row[status]-{$key}"><input id="row[status]-{$key}" name="row[status]" type="radio" value="{$key}" {in name="key" value="$row.status"}checked{/in} /> {$vo}</label> 
            {/foreach}
            </div>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Platform')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-platform" class="form-control" name="row[platform]" type="text" value="{$row.platform|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Payment_json')}:</label>
        <div class="col-xs-12 col-sm-8">
            
            <dl class="fieldlist" data-name="row[payment_json]">
                <dd>
                    <ins>{:__('Key')}</ins>
                    <ins>{:__('Value')}</ins>
                </dd>
                <dd><a href="javascript:;" class="btn btn-sm btn-success btn-append"><i class="fa fa-plus"></i> {:__('Append')}</a></dd>
                <textarea name="row[payment_json]" class="form-control hide" cols="30" rows="5">{$row.payment_json|htmlentities}</textarea>
            </dl>


        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Memo')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-memo" class="form-control" name="row[memo]" type="text" value="{$row.memo|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Log')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-log" class="form-control " rows="5" name="row[log]" cols="50">{$row.log|htmlentities}</textarea>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Image')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-image" class="form-control" size="50" name="row[image]" type="text" value="{$row.image|htmlentities}">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-image" class="btn btn-danger faupload" data-input-id="c-image" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp" data-multiple="false" data-preview-id="p-image"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-image" class="btn btn-primary fachoose" data-input-id="c-image" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-image"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-image"></ul>
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
