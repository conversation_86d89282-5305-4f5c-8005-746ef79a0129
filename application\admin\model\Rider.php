<?php

namespace app\admin\model;

use think\Model;


class Rider extends Model
{

    

    

    // 表名
    protected $name = 'rider';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'current_status_text',
        'last_location_time_text',
        'status_text'
    ];
    

    
    public function getCurrentStatusList()
    {
        return ['0' => __('Current_status 0'), '1' => __('Current_status 1'), '2' => __('Current_status 2')];
    }

    public function getStatusList()
    {
        return ['1' => __('Status 1'), '0' => __('Status 0')];
    }


    public function getCurrentStatusTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['current_status']) ? $data['current_status'] : '');
        $list = $this->getCurrentStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getLastLocationTimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['last_location_time']) ? $data['last_location_time'] : '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }


    public function getStatusTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['status']) ? $data['status'] : '');
        $list = $this->getStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }

    protected function setLastLocationTimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }


    public function agent()
    {
        return $this->belongsTo('app\admin\model\vending\Agent', 'agent_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}
