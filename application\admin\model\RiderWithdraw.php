<?php

namespace app\admin\model;

use think\Model;


class RiderWithdraw extends Model
{

    

    

    // 表名
    protected $name = 'rider_withdraw';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'apply_type_text',
        'status_text'
    ];
    

    
    public function getApplyTypeList()
    {
        return ['bank' => __('Bank'), 'wechat' => __('Wechat'), 'alipay' => __('Alipay')];
    }

    public function getStatusList()
    {
        return ['-1' => __('Status -1'), '0' => __('Status 0'), '1' => __('Status 1'), '2' => __('Status 2'), '3' => __('Status 3')];
    }


    public function getApplyTypeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['apply_type']) ? $data['apply_type'] : '');
        $list = $this->getApplyTypeList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getStatusTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['status']) ? $data['status'] : '');
        $list = $this->getStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }




    public function agent()
    {
        return $this->belongsTo('app\admin\model\vending\Agent', 'agent_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }


    public function rider()
    {
        return $this->belongsTo('Rider', 'rider_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}
