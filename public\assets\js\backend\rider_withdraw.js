define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'rider_withdraw/index' + location.search,
                    add_url: 'rider_withdraw/add',
                    edit_url: 'rider_withdraw/edit',
                    del_url: 'rider_withdraw/del',
                    multi_url: 'rider_withdraw/multi',
                    import_url: 'rider_withdraw/import',
                    table: 'rider_withdraw',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                fixedColumns: true,
                fixedRightNumber: 1,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'agent_id', title: __('Agent_id')},
                        {field: 'rider_id', title: __('Rider_id')},
                        {field: 'order_sn', title: __('Order_sn'), operate: 'LIKE'},
                        {field: 'apply_type', title: __('Apply_type'), searchList: {"bank":__('Bank'),"wechat":__('Wechat'),"alipay":__('Alipay')}, formatter: Table.api.formatter.normal},
                        {field: 'amount', title: __('Amount'), operate:'BETWEEN'},
                        {field: 'real_amount', title: __('Real_amount'), operate:'BETWEEN'},
                        {field: 'service_fee_amount', title: __('Service_fee_amount'), operate:'BETWEEN'},
                        {field: 'service_fee_rate', title: __('Service_fee_rate'), operate:'BETWEEN'},
                        {field: 'username', title: __('Username'), operate: 'LIKE'},
                        {field: 'card_no', title: __('Card_no'), operate: 'LIKE'},
                        {field: 'apply_info', title: __('Apply_info'), operate: 'LIKE'},
                        {field: 'status', title: __('Status'), searchList: {"-1":__('Status -1'),"0":__('Status 0'),"1":__('Status 1'),"2":__('Status 2'),"3":__('Status 3')}, formatter: Table.api.formatter.status},
                        {field: 'platform', title: __('Platform'), operate: 'LIKE'},
                        {field: 'memo', title: __('Memo'), operate: 'LIKE'},
                        {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'updatetime', title: __('Updatetime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'image', title: __('Image'), operate: false, events: Table.api.events.image, formatter: Table.api.formatter.image},
                        {field: 'agent.name', title: __('Agent.name'), operate: 'LIKE'},
                        {field: 'rider.real_name', title: __('Rider.real_name'), operate: 'LIKE'},
                        {field: 'rider.mobile', title: __('Rider.mobile'), operate: 'LIKE'},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
